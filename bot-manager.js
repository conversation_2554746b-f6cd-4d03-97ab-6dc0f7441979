/**
 * 🤖 نظام إدارة البوتات المتقدم
 * إدارة شاملة لبوتات ماينكرافت Java و Bedrock
 */

const { EventEmitter } = require('events');
const DatabaseManager = require('./database');
const MinecraftJavaBot = require('./minecraft-java-bot');
const MinecraftBedrockBot = require('./minecraft-bedrock-bot');
const { config } = require('./config');

class BotManager extends EventEmitter {
    constructor() {
        super();
        this.db = null;
        this.activeBots = new Map(); // botId -> bot instance
        this.botStats = new Map(); // botId -> stats
        this.botStates = new Map(); // botId -> detailed state info
        this.userBots = new Map(); // userId -> Set of botIds
        this.reconnectTimers = new Map(); // botId -> timer
        this.alertTimers = new Map(); // botId -> alert timer
        this.disconnectionAlerts = new Map(); // botId -> alert count
        this.manuallyStoppedBots = new Set(); // manually stopped bots
        this.monitoringInterval = null;
        this.initialized = false;

        // إعدادات البوتات من الكونفيج
        this.supportedVersions = config.supportedVersions;
        this.maxBotsPerUser = config.bots.maxBotsPerUser;
        this.reconnectionConfig = config.bots.reconnection;
        this.timeoutConfig = config.bots.timeout;
        this.monitoringConfig = config.bots.monitoring;
    }

    // تهيئة مدير البوتات
    async init() {
        if (this.initialized) return this;

        try {
            console.log('🤖 تهيئة مدير البوتات...');
            
            // تهيئة قاعدة البيانات
            this.db = new DatabaseManager();
            await this.db.init();

            // بدء مراقبة البوتات
            if (this.monitoringConfig.enabled) {
                this.startMonitoring();
            }

            // بدء تنظيف الإحصائيات القديمة
            this.startStatsCleanup();

            this.initialized = true;
            console.log('✅ تم تهيئة مدير البوتات بنجاح');
            
            return this;
        } catch (error) {
            console.error('❌ خطأ في تهيئة مدير البوتات:', error);
            throw error;
        }
    }

    // بدء مراقبة البوتات
    startMonitoring() {
        if (this.monitoringInterval) return;

        this.monitoringInterval = setInterval(async () => {
            await this.checkAllBots();
        }, this.monitoringConfig.interval);

        console.log(`🔍 تم بدء مراقبة البوتات (كل ${this.monitoringConfig.interval / 1000} ثانية)`);
    }

    // إيقاف مراقبة البوتات
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
            console.log('⏹️ تم إيقاف مراقبة البوتات');
        }
    }

    // فحص جميع البوتات
    async checkAllBots() {
        try {
            for (const [botId, bot] of this.activeBots.entries()) {
                await this.checkBotHealth(botId, bot);
            }
        } catch (error) {
            console.error('❌ خطأ في فحص البوتات:', error);
        }
    }

    // فحص صحة بوت معين
    async checkBotHealth(botId, bot) {
        try {
            const isAlive = bot && bot.isAlive();
            const botData = await this.db.getBot(botId);

            if (!botData) {
                console.warn(`⚠️ بوت غير موجود في قاعدة البيانات: ${botId}`);
                this.activeBots.delete(botId);
                return;
            }

            // تحديث الإحصائيات
            this.updateBotStats(botId, {
                isAlive,
                lastCheck: new Date(),
                uptime: bot.getUptime ? bot.getUptime() : 0
            });

            // إذا كان البوت منقطع
            if (!isAlive) {
                await this.handleBotDisconnection(botId, botData);
            } else {
                // إعادة تعيين التحذيرات عند الاتصال
                this.clearBotAlerts(botId);
            }
        } catch (error) {
            console.error(`❌ خطأ في فحص البوت ${botId}:`, error);
        }
    }

    // معالجة انقطاع البوت
    async handleBotDisconnection(botId, botData) {
        // تجنب التحذيرات المتكررة للبوتات المتوقفة يدوياً
        if (this.manuallyStoppedBots.has(botId)) {
            return;
        }

        console.log(`🔌 البوت ${botData.bot_name} منقطع - بدء نظام التحذيرات`);

        // بدء نظام التحذيرات إذا لم يكن نشطاً
        if (!this.alertTimers.has(botId)) {
            await this.startAlertSystem(botId, botData);
        }
    }

    // بدء نظام التحذيرات
    async startAlertSystem(botId, botData) {
        console.log(`🔄 بدء نظام التحذيرات للبوت ${botData.bot_name}`);

        // إرسال التحذير الأول فوراً
        this.disconnectionAlerts.set(botId, 1);
        this.emit('serverDown', {
            botId: botId,
            botName: botData.bot_name,
            host: botData.server_host,
            port: botData.server_port,
            alertCount: 1,
            userId: botData.user_id,
            timeRemaining: 4 // 4 دقائق متبقية
        });

        // بدء مؤقت التحذيرات (كل دقيقة)
        let alertCount = 1;
        const alertTimer = setInterval(async () => {
            // فحص إذا كان البوت عاد للاتصال
            if (!this.alertTimers.has(botId)) {
                clearInterval(alertTimer);
                return;
            }

            alertCount++;
            if (alertCount <= 5) {
                console.log(`⚠️ إرسال التحذير ${alertCount}/5 للبوت ${botData.bot_name}`);

                this.disconnectionAlerts.set(botId, alertCount);
                this.emit('serverDown', {
                    botId: botId,
                    botName: botData.bot_name,
                    host: botData.server_host,
                    port: botData.server_port,
                    alertCount: alertCount,
                    userId: botData.user_id,
                    timeRemaining: 5 - alertCount
                });

                // إذا وصلنا للتحذير الخامس، نوقف البوت
                if (alertCount === 5) {
                    console.log(`🛑 إيقاف البوت ${botData.bot_name} نهائياً بعد 5 تحذيرات`);

                    this.emit('serverDownFinal', {
                        botId: botId,
                        botName: botData.bot_name,
                        host: botData.server_host,
                        port: botData.server_port,
                        userId: botData.user_id
                    });

                    // تنظيف وإيقاف البوت
                    clearInterval(alertTimer);
                    this.alertTimers.delete(botId);
                    this.disconnectionAlerts.delete(botId);
                    await this.stopBot(botId);
                }
            }
        }, 60000); // كل دقيقة

        // حفظ المؤقت
        this.alertTimers.set(botId, alertTimer);
    }

    // تنظيف تحذيرات البوت
    clearBotAlerts(botId) {
        if (this.alertTimers.has(botId)) {
            clearInterval(this.alertTimers.get(botId));
            this.alertTimers.delete(botId);
        }

        if (this.disconnectionAlerts.has(botId)) {
            this.disconnectionAlerts.delete(botId);
        }

        if (this.reconnectTimers.has(botId)) {
            clearTimeout(this.reconnectTimers.get(botId));
            this.reconnectTimers.delete(botId);
        }
    }

    // تحديث إحصائيات البوت
    updateBotStats(botId, stats) {
        if (!this.botStats.has(botId)) {
            this.botStats.set(botId, {
                startTime: new Date(),
                totalUptime: 0,
                connectionCount: 0,
                messagesSent: 0,
                commandsExecuted: 0,
                errorsCount: 0
            });
        }

        const currentStats = this.botStats.get(botId);
        Object.assign(currentStats, stats);
        this.botStats.set(botId, currentStats);
    }

    // الحصول على إحصائيات البوت
    getBotStats(botId) {
        return this.botStats.get(botId) || null;
    }

    // إدارة حالة البوتات لكل مستخدم
    addUserBot(userId, botId) {
        if (!this.userBots.has(userId)) {
            this.userBots.set(userId, new Set());
        }
        this.userBots.get(userId).add(botId);
    }

    removeUserBot(userId, botId) {
        if (this.userBots.has(userId)) {
            this.userBots.get(userId).delete(botId);
            if (this.userBots.get(userId).size === 0) {
                this.userBots.delete(userId);
            }
        }
    }

    getUserActiveBots(userId) {
        const userBotIds = this.userBots.get(userId) || new Set();
        const activeBots = [];
        for (const botId of userBotIds) {
            if (this.activeBots.has(botId)) {
                activeBots.push({
                    botId,
                    bot: this.activeBots.get(botId),
                    state: this.botStates.get(botId)
                });
            }
        }
        return activeBots;
    }

    // تحديث حالة البوت
    updateBotState(botId, state) {
        const currentState = this.botStates.get(botId) || {};
        this.botStates.set(botId, { ...currentState, ...state, lastUpdate: new Date() });
    }

    // فحص وجود بوت نشط مكرر
    async checkForActiveDuplicateBot(botData) {
        try {
            for (const [activeBotId, activeBot] of this.activeBots.entries()) {
                if (activeBotId === botData.id) continue;

                const activeBotData = await this.db.getBot(activeBotId);
                if (activeBotData &&
                    activeBotData.user_id === botData.user_id && // فحص نفس المستخدم فقط
                    activeBotData.bot_name === botData.bot_name &&
                    activeBotData.server_host === botData.server_host &&
                    activeBotData.server_port === botData.server_port &&
                    activeBot.isAlive()) {

                    console.log(`⚠️ تم العثور على بوت نشط مكرر للمستخدم ${botData.user_id}: ${activeBotData.bot_name}`);
                    return activeBotData;
                }
            }

            return null;
        } catch (error) {
            console.error('❌ خطأ في فحص البوتات المكررة:', error);
            return null;
        }
    }

    // فحص وجود بوت بنفس الاسم في نفس السيرفر لنفس المستخدم في قاعدة البيانات
    async checkForDuplicateBotInDB(userId, botName, serverHost, serverPort, excludeBotId = null) {
        try {
            const existingBot = await this.db.getBotByUserAndDetails(userId, botName, serverHost, serverPort);
            if (existingBot && existingBot.id !== excludeBotId) {
                return existingBot;
            }
            return null;
        } catch (error) {
            console.error('❌ خطأ في فحص البوت المكرر في قاعدة البيانات:', error);
            return null;
        }
    }

    // التحقق من صحة إعدادات البوت
    validateBotConfig(botConfig) {
        const errors = [];

        // التحقق من الحقول المطلوبة
        if (!botConfig.name || botConfig.name.trim().length === 0) {
            errors.push('اسم البوت مطلوب');
        }

        if (!botConfig.host || botConfig.host.trim().length === 0) {
            errors.push('عنوان السيرفر مطلوب');
        }

        if (!botConfig.port || isNaN(botConfig.port) || botConfig.port < 1 || botConfig.port > 65535) {
            errors.push('رقم المنفذ غير صحيح');
        }

        if (!botConfig.version || !this.supportedVersions[botConfig.edition]?.includes(botConfig.version)) {
            errors.push('إصدار ماينكرافت غير مدعوم');
        }

        if (!['java', 'bedrock'].includes(botConfig.edition)) {
            errors.push('نوع ماينكرافت غير صحيح');
        }

        // التحقق من طول الاسم
        if (botConfig.name && botConfig.name.length > 16) {
            errors.push('اسم البوت يجب أن يكون أقل من 16 حرف');
        }

        // التحقق من صحة اسم البوت (أحرف وأرقام فقط)
        if (botConfig.name && !/^[a-zA-Z0-9_]+$/.test(botConfig.name)) {
            errors.push('اسم البوت يجب أن يحتوي على أحرف وأرقام فقط');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    // بدء تنظيف الإحصائيات القديمة
    startStatsCleanup() {
        // تنظيف الإحصائيات كل ساعة
        setInterval(async () => {
            await this.cleanupOldStats();
        }, 3600000); // ساعة واحدة

        console.log('🧹 تم بدء تنظيف الإحصائيات التلقائي');
    }

    // تنظيف الإحصائيات القديمة
    async cleanupOldStats() {
        try {
            const retentionPeriod = config.statistics.retention || 2592000000; // 30 يوم
            const cutoffDate = new Date(Date.now() - retentionPeriod);

            // حذف الإحصائيات القديمة من قاعدة البيانات
            // سيتم تنفيذ هذا لاحقاً حسب نوع قاعدة البيانات

            console.log('🧹 تم تنظيف الإحصائيات القديمة');
        } catch (error) {
            console.error('❌ خطأ في تنظيف الإحصائيات:', error);
        }
    }

    // ==========================================
    // دوال إدارة البوتات الأساسية
    // ==========================================

    // إنشاء بوت جديد
    async createBot(userId, botConfig) {
        try {
            // التحقق من صحة الإعدادات
            const validation = this.validateBotConfig(botConfig);
            if (!validation.valid) {
                throw new Error(validation.errors.join(', '));
            }

            // التحقق من عدد البوتات المسموح للمستخدم
            const userBots = await this.db.getUserBots(userId);
            if (userBots.length >= this.maxBotsPerUser) {
                throw new Error(`لا يمكنك إنشاء أكثر من ${this.maxBotsPerUser} بوتات`);
            }

            // فحص وجود بوت مكرر لنفس المستخدم
            const duplicateBot = await this.checkForDuplicateBotInDB(
                userId,
                botConfig.name,
                botConfig.host,
                botConfig.port
            );
            if (duplicateBot) {
                throw new Error(`يوجد بوت بالاسم "${botConfig.name}" في السيرفر ${botConfig.host}:${botConfig.port} مسبقاً`);
            }

            // إنشاء البوت في قاعدة البيانات
            const botId = await this.db.createBot(userId, {
                botName: botConfig.name,
                serverHost: botConfig.host,
                serverPort: botConfig.port,
                minecraftVersion: botConfig.version,
                edition: botConfig.edition
            });

            console.log(`✅ تم إنشاء البوت بنجاح للمستخدم ${userId} - ID: ${botId}`);
            return { success: true, botId, message: 'تم إنشاء البوت بنجاح' };

        } catch (error) {
            console.error('❌ خطأ في إنشاء البوت:', error);
            return { success: false, error: error.message };
        }
    }

    // بدء تشغيل البوت
    async startBot(botId) {
        try {
            // التحقق من وجود البوت في قاعدة البيانات
            const botData = await this.db.getBot(botId);
            if (!botData) {
                throw new Error('البوت غير موجود');
            }

            // التحقق من أن البوت ليس يعمل بالفعل
            if (this.activeBots.has(botId)) {
                throw new Error('البوت يعمل بالفعل');
            }

            // فحص وجود بوت نشط آخر بنفس الاسم في نفس السيرفر
            const duplicateActiveBot = await this.checkForActiveDuplicateBot(botData);
            if (duplicateActiveBot) {
                throw new Error(`يوجد بوت نشط آخر بالاسم "${botData.bot_name}" في السيرفر ${botData.server_host}:${botData.server_port}`);
            }

            // إزالة البوت من قائمة الإيقاف اليدوي
            if (this.manuallyStoppedBots.has(botId)) {
                this.manuallyStoppedBots.delete(botId);
            }

            // إنشاء instance البوت
            const botConfig = {
                host: botData.server_host,
                port: botData.server_port,
                username: botData.bot_name,
                version: botData.minecraft_version
            };

            let bot;
            if (botData.edition === 'java') {
                bot = new MinecraftJavaBot(botConfig);
            } else if (botData.edition === 'bedrock') {
                bot = new MinecraftBedrockBot(botConfig);
            } else {
                throw new Error('نوع البوت غير مدعوم');
            }

            // إعداد مستمعي الأحداث
            this.setupBotEventListeners(bot, botId, botData);

            // بدء الاتصال
            await bot.connect();

            // حفظ البوت في الذاكرة
            this.activeBots.set(botId, bot);

            // إضافة البوت لقائمة بوتات المستخدم
            this.addUserBot(botData.user_id, botId);

            // تحديث حالة البوت الداخلية
            this.updateBotState(botId, {
                status: 'connecting',
                userId: botData.user_id,
                botName: botData.bot_name,
                serverHost: botData.server_host,
                serverPort: botData.server_port,
                edition: botData.edition,
                startTime: new Date()
            });

            // تحديث حالة البوت في قاعدة البيانات
            await this.db.updateBotStatus(botId, 'connecting', {
                last_connected: new Date().toISOString(),
                connection_count: botData.connection_count + 1
            });

            // تهيئة الإحصائيات
            this.updateBotStats(botId, {
                startTime: new Date(),
                connectionCount: botData.connection_count + 1
            });

            console.log(`🚀 تم بدء تشغيل البوت ${botData.bot_name} للمستخدم ${botData.user_id}`);

            // إرسال إشارة بدء التشغيل
            this.emit('botStarted', {
                botId,
                userId: botData.user_id,
                botName: botData.bot_name
            });

            return { success: true, message: 'تم بدء تشغيل البوت بنجاح' };

        } catch (error) {
            console.error('❌ خطأ في بدء تشغيل البوت:', error);
            return { success: false, error: error.message };
        }
    }

    // إيقاف البوت
    async stopBot(botId) {
        try {
            const botData = await this.db.getBot(botId);
            if (!botData) {
                throw new Error('البوت غير موجود');
            }

            // إضافة البوت لقائمة الإيقاف اليدوي
            this.manuallyStoppedBots.add(botId);

            // تنظيف التحذيرات
            this.clearBotAlerts(botId);

            // إيقاف البوت إذا كان نشطاً
            if (this.activeBots.has(botId)) {
                const bot = this.activeBots.get(botId);
                await bot.disconnect();
                this.activeBots.delete(botId);
            }

            // إزالة البوت من قائمة بوتات المستخدم
            this.removeUserBot(botData.user_id, botId);

            // تحديث حالة البوت الداخلية
            this.updateBotState(botId, {
                status: 'stopped',
                stopTime: new Date()
            });

            // تحديث حالة البوت في قاعدة البيانات
            await this.db.updateBotStatus(botId, 'stopped');

            // حفظ إحصائيات الجلسة
            const stats = this.getBotStats(botId);
            if (stats) {
                await this.db.addBotStat(botId, stats.startTime, new Date(), {
                    messages_sent: stats.messagesSent,
                    commands_executed: stats.commandsExecuted,
                    errors_count: stats.errorsCount
                });
            }

            // إزالة الإحصائيات والحالة من الذاكرة
            this.botStats.delete(botId);
            this.botStates.delete(botId);

            console.log(`⏹️ تم إيقاف البوت ${botData.bot_name} للمستخدم ${botData.user_id}`);

            // إرسال إشارة إيقاف البوت
            this.emit('botStopped', {
                botId,
                userId: botData.user_id,
                botName: botData.bot_name
            });

            return { success: true, message: 'تم إيقاف البوت بنجاح' };

        } catch (error) {
            console.error('❌ خطأ في إيقاف البوت:', error);
            return { success: false, error: error.message };
        }
    }

    // حذف البوت
    async deleteBot(botId) {
        try {
            // إيقاف البوت أولاً
            await this.stopBot(botId);

            // حذف البوت من قاعدة البيانات
            const result = await this.db.deleteBot(botId);

            if (result === 0) {
                throw new Error('البوت غير موجود');
            }

            console.log(`🗑️ تم حذف البوت ${botId} بنجاح`);
            return { success: true, message: 'تم حذف البوت بنجاح' };

        } catch (error) {
            console.error('❌ خطأ في حذف البوت:', error);
            return { success: false, error: error.message };
        }
    }

    // إرسال رسالة عبر البوت
    async sendMessage(botId, message) {
        try {
            const bot = this.activeBots.get(botId);
            if (!bot || !bot.isAlive()) {
                throw new Error('البوت غير متصل');
            }

            const result = bot.sendMessage(message);
            if (result) {
                // تحديث الإحصائيات
                const stats = this.getBotStats(botId);
                if (stats) {
                    stats.messagesSent++;
                    this.updateBotStats(botId, stats);
                }
            }

            return { success: result, message: result ? 'تم إرسال الرسالة' : 'فشل في إرسال الرسالة' };

        } catch (error) {
            console.error('❌ خطأ في إرسال الرسالة:', error);
            return { success: false, error: error.message };
        }
    }

    // تنفيذ أمر عبر البوت
    async executeCommand(botId, command) {
        try {
            const bot = this.activeBots.get(botId);
            if (!bot || !bot.isAlive()) {
                throw new Error('البوت غير متصل');
            }

            const result = bot.executeCommand(command);
            if (result) {
                // تحديث الإحصائيات
                const stats = this.getBotStats(botId);
                if (stats) {
                    stats.commandsExecuted++;
                    this.updateBotStats(botId, stats);
                }
            }

            return { success: result, message: result ? 'تم تنفيذ الأمر' : 'فشل في تنفيذ الأمر' };

        } catch (error) {
            console.error('❌ خطأ في تنفيذ الأمر:', error);
            return { success: false, error: error.message };
        }
    }

    // الحصول على حالة البوت
    async getBotStatus(botId) {
        try {
            const botData = await this.db.getBot(botId);
            if (!botData) {
                return { success: false, error: 'البوت غير موجود' };
            }

            const isActive = this.activeBots.has(botId);
            const bot = this.activeBots.get(botId);
            const isAlive = bot ? bot.isAlive() : false;
            const stats = this.getBotStats(botId);

            return {
                success: true,
                status: {
                    ...botData,
                    isActive,
                    isAlive,
                    stats: stats || null,
                    uptime: bot && bot.getUptime ? bot.getUptime() : 0
                }
            };

        } catch (error) {
            console.error('❌ خطأ في الحصول على حالة البوت:', error);
            return { success: false, error: error.message };
        }
    }

    // إعداد مستمعي أحداث البوت
    setupBotEventListeners(bot, botId, botData) {
        bot.on('connected', async (data) => {
            console.log(`✅ البوت ${botData.bot_name} متصل بنجاح`);

            // تحديث حالة البوت الداخلية
            this.updateBotState(botId, {
                status: 'running',
                connectedAt: new Date(),
                lastActivity: new Date()
            });

            // تحديث حالة البوت في قاعدة البيانات
            await this.db.updateBotStatus(botId, 'running');

            // تنظيف التحذيرات
            this.clearBotAlerts(botId);

            this.emit('botConnected', {
                botId,
                userId: botData.user_id,
                botName: botData.bot_name,
                shouldDeleteWarnings: true,
                ...data
            });
        });

        // عند دخول العالم بنجاح
        bot.on('worldJoined', async (data) => {
            console.log(`🌍 البوت ${botData.bot_name} دخل إلى العالم بنجاح`);

            // تنظيف التحذيرات نهائياً
            this.clearBotAlerts(botId);

            // إرسال رسالة نجاح دخول العالم
            this.emit('botWorldJoined', {
                botId,
                botName: botData.bot_name,
                userId: botData.user_id,
                serverHost: botData.server_host,
                serverPort: botData.server_port,
                edition: botData.edition,
                shouldDeleteWarnings: true,
                ...data
            });
        });

        bot.on('disconnected', async (data) => {
            console.log(`🔌 البوت ${botData.bot_name} انقطع الاتصال`);

            // تحديث حالة البوت
            if (!this.manuallyStoppedBots.has(botId)) {
                await this.db.updateBotStatus(botId, 'connecting');
            } else {
                await this.db.updateBotStatus(botId, 'stopped');
            }

            this.emit('botDisconnected', { botId, botName: botData.bot_name, ...data });

            // بدء عد الانقطاع إذا لم يكن متوقف يدوياً
            if (!this.manuallyStoppedBots.has(botId)) {
                await this.handleBotDisconnection(botId, botData);
            }
        });

        // عند عدم توافق الإصدار
        bot.on('versionMismatch', async (data) => {
            console.log(`⚠️ عدم توافق إصدار للبوت ${botData.bot_name}: ${data.botVersion}`);

            this.emit('botVersionMismatch', {
                botId: botId,
                botName: botData.bot_name,
                botVersion: data.botVersion,
                serverMessage: data.serverMessage,
                userId: botData.user_id
            });
        });

        bot.on('kicked', async (data) => {
            const reason = data.reason || 'سبب غير معروف';
            console.log(`👢 تم ركل البوت ${botData.bot_name}: ${reason}`);

            // فحص إذا كان السبب هو وجود بوت آخر بنفس الاسم
            if (data.isLoggedInOtherLocation) {
                console.log(`🚫 البوت ${botData.bot_name} تم ركله لوجود بوت آخر بنفس الاسم`);

                this.emit('botDuplicateName', {
                    botId: botId,
                    botName: botData.bot_name,
                    host: botData.server_host,
                    port: botData.server_port,
                    userId: botData.user_id,
                    reason: reason
                });

                await this.stopBot(botId);
                return;
            }

            // معالجة أسباب الركل الأخرى
            this.emit('botKicked', { botId, botName: botData.bot_name, reason, ...data });
        });

        bot.on('error', async (error) => {
            console.error(`❌ خطأ في البوت ${botData.bot_name}: ${error.message}`);

            // تحديث الإحصائيات
            const stats = this.getBotStats(botId);
            if (stats) {
                stats.errorsCount++;
                this.updateBotStats(botId, stats);
            }

            await this.db.updateBotStatus(botId, 'error');
            this.emit('botError', { botId, botName: botData.bot_name, error: error.message });
        });
    }

    // إغلاق مدير البوتات
    async close() {
        try {
            console.log('🔒 إغلاق مدير البوتات...');

            // إيقاف المراقبة
            this.stopMonitoring();

            // إيقاف جميع البوتات
            for (const [botId, bot] of this.activeBots.entries()) {
                try {
                    await bot.disconnect();
                } catch (error) {
                    console.error(`❌ خطأ في إيقاف البوت ${botId}:`, error);
                }
            }

            // تنظيف جميع المؤقتات
            for (const timer of this.alertTimers.values()) {
                clearInterval(timer);
            }
            for (const timer of this.reconnectTimers.values()) {
                clearTimeout(timer);
            }

            // إغلاق قاعدة البيانات
            if (this.db) {
                await this.db.close();
            }

            console.log('✅ تم إغلاق مدير البوتات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في إغلاق مدير البوتات:', error);
        }
    }
}

module.exports = BotManager;
