/**
 * ☕ بوت ماينكرافت Java Edition المحسن
 * دعم كامل لجميع ميزات Java Edition مع إعادة اتصال ذكية
 */

const mineflayer = require('mineflayer');
const { EventEmitter } = require('events');
const { config } = require('./config');

class MinecraftJavaBot extends EventEmitter {
    constructor(botConfig) {
        super();
        this.config = botConfig;
        this.bot = null;
        this.isConnected = false;
        this.connectionTime = null;
        this.disconnectionTime = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 30; // 5 دقائق × 6 محاولات/دقيقة
        this.reconnectDelay = config.bots.reconnection.delay;
        this.shouldReconnect = true;
        this.reconnectTimeout = null;
        this.isConnecting = false; // منع المحاولات المتعددة
        this.stats = {
            messagesSent: 0,
            commandsExecuted: 0,
            deaths: 0,
            respawns: 0,
            errors: 0
        };
        
        console.log(`🔧 تم إنشاء بوت Java: ${this.config.username}`);
    }

    // الاتصال بالسيرفر
    async connect() {
        // منع المحاولات المتعددة
        if (this.isConnecting) {
            console.log(`⚠️ محاولة اتصال جارية بالفعل للبوت ${this.config.username}`);
            return;
        }

        this.isConnecting = true;

        try {
            console.log(`🔄 محاولة الاتصال بسيرفر Java: ${this.config.host}:${this.config.port}`);
            console.log(`📦 إصدار البوت: ${this.config.version}`);
            
            const botOptions = {
                host: this.config.host,
                port: this.config.port,
                username: this.config.username,
                version: this.config.version,
                auth: 'offline', // وضع offline للسيرفرات المحلية
                hideErrors: false,
                skipValidation: true,
                checkTimeoutInterval: 30000,
                keepAlive: true,
                connectTimeout: 15000, // 15 ثانية للاتصال
                timeout: 10000 // 10 ثواني للاستجابة
            };

            // استخدام الإصدار الذي اختاره المستخدم دائماً
            botOptions.version = this.config.version;
            console.log(`🎯 استخدام إصدار البوت المحدد: ${this.config.version}`);

            this.bot = mineflayer.createBot(botOptions);
            this.setupEventHandlers();

            // إضافة timeout للاتصال الأولي
            const connectionTimeout = setTimeout(() => {
                if (this.isConnecting && !this.isConnected) {
                    console.log(`⏰ انتهت مهلة الاتصال للبوت ${this.config.username}`);
                    this.isConnecting = false;

                    if (this.bot) {
                        this.bot.end();
                    }

                    // محاولة إعادة الاتصال
                    if (this.shouldReconnect) {
                        this.handleReconnect();
                    }
                }
            }, 30000); // 30 ثانية timeout

            // إلغاء timeout عند نجاح الاتصال
            this.bot.once('spawn', () => {
                clearTimeout(connectionTimeout);
            });

            // إلغاء timeout عند حدوث خطأ
            this.bot.once('error', () => {
                clearTimeout(connectionTimeout);
            });

        } catch (error) {
            this.isConnecting = false; // إنهاء حالة الاتصال
            console.error(`❌ خطأ في إنشاء بوت Java: ${error.message}`);
            // بدء إعادة المحاولة إذا كان السبب يشير لسيرفر مطفي
            if (this.isServerDownReason(error.message)) {
                this.handleReconnect();
            } else {
                this.emit('error', error);
            }
        }
    }

    // إعداد معالجات الأحداث
    setupEventHandlers() {
        // عند الاتصال بنجاح (login فقط - ليس دخول العالم بعد)
        this.bot.on('login', () => {
            console.log(`✅ تم الاتصال بنجاح بسيرفر Java ${this.config.host}:${this.config.port}`);
            console.log(`⏳ انتظار دخول العالم...`);
            this.isConnecting = false; // إنهاء حالة الاتصال
            this.connectionTime = new Date();
            this.reconnectAttempts = 0;

            // تنظيف timeout إعادة الاتصال إذا كان موجود
            if (this.reconnectTimeout) {
                clearTimeout(this.reconnectTimeout);
                this.reconnectTimeout = null;
            }

            // لا نرسل connected هنا - ننتظر spawn
        });

        // عند دخول العالم - هنا نرسل إشارة الاتصال الكامل
        this.bot.on('spawn', () => {
            console.log(`🌍 دخل البوت ${this.config.username} إلى عالم Java`);
            console.log(`✅ البوت ${this.config.username} متصل ودخل العالم بنجاح`);

            // الآن البوت متصل فعلياً ودخل العالم
            this.isConnected = true;

            // إرسال إشارة الاتصال الكامل بعد دخول العالم
            this.emit('connected', {
                connectionTime: this.connectionTime,
                serverInfo: {
                    host: this.config.host,
                    port: this.config.port,
                    version: this.bot.version
                },
                worldInfo: {
                    position: this.bot.entity?.position,
                    dimension: this.bot.game?.dimension,
                    gameMode: this.bot.game?.gameMode
                }
            });

            this.emit('worldJoined', {
                position: this.bot.entity?.position,
                dimension: this.bot.game?.dimension,
                gameMode: this.bot.game?.gameMode
            });
        });

        // عند استقبال رسالة في الشات
        this.bot.on('chat', (username, message) => {
            if (username === this.bot.username) return;
            
            console.log(`💬 [${username}]: ${message}`);
            this.emit('chatMessage', {
                username,
                message,
                timestamp: new Date()
            });
        });

        // عند انقطاع الاتصال
        this.bot.on('end', (reason) => {
            console.log(`🔌 البوت ${this.config.username} انقطع الاتصال`);
            if (reason) {
                console.log(`📝 سبب الانقطاع: ${reason}`);
            }
            console.log(`🔌 السيرفر ${this.config.host}:${this.config.port} مطفي أو غير متصل`);

            this.isConnected = false;
            this.isConnecting = false; // إعادة تعيين حالة الاتصال
            this.disconnectionTime = new Date();

            this.emit('disconnected', {
                reason: reason || 'انقطاع الاتصال',
                connectionTime: this.connectionTime,
                disconnectionTime: this.disconnectionTime,
                duration: this.getSessionDuration()
            });

            // محاولة إعادة الاتصال فقط إذا لم يكن إيقاف يدوي
            if (this.shouldReconnect) {
                console.log(`🔄 بدء إعادة الاتصال بسبب انقطاع الاتصال`);
                this.handleReconnect();
            }
        });

        // عند الركل من السيرفر
        this.bot.on('kicked', (reason, loggedIn) => {
            console.log(`👢 تم ركل البوت من سيرفر Java: ${reason}`);
            this.isConnected = false;
            this.isConnecting = false; // إعادة تعيين حالة الاتصال

            // تحديد نوع الانقطاع
            const isServerDown = this.isServerDownReason(reason);
            const isLoggedInOtherLocation = this.isLoggedInOtherLocationReason(reason);
            const isVersionMismatch = reason.toLowerCase().includes('outdated') ||
                                    reason.toLowerCase().includes('version') ||
                                    reason.toLowerCase().includes('protocol');

            this.emit('kicked', {
                reason: reason,
                loggedIn: loggedIn,
                isServerDown: isServerDown,
                isLoggedInOtherLocation: isLoggedInOtherLocation,
                isVersionMismatch: isVersionMismatch
            });

            // إيقاف محاولات إعادة الاتصال إذا كان السبب هو وجود بوت آخر بنفس الاسم
            if (isLoggedInOtherLocation) {
                console.log(`🚫 إيقاف محاولات إعادة الاتصال للبوت ${this.config.username} - يوجد بوت آخر بنفس الاسم`);
                this.shouldReconnect = false;
                this.stopReconnecting();
                return;
            }

            // إذا كان السبب هو عدم توافق الإصدار، نرسل تحذير خاص
            if (isVersionMismatch) {
                console.log(`⚠️ تحذير: إصدار البوت ${this.config.version} غير متوافق مع السيرفر`);
                console.log(`⚠️ رسالة السيرفر: ${reason}`);

                // إرسال حدث خاص بعدم توافق الإصدار
                this.emit('versionMismatch', {
                    botVersion: this.config.version,
                    serverMessage: reason
                });
            }

            // محاولة إعادة الاتصال
            console.log(`🔄 بدء إعادة المحاولة للبوت ${this.config.username} بسبب: ${reason}`);
            this.handleReconnect();
        });

        // عند الموت
        this.bot.on('death', () => {
            console.log(`💀 مات البوت ${this.config.username}`);
            this.stats.deaths++;
            
            this.emit('death', {
                position: this.bot.entity.position,
                killer: this.bot.lastDamageSource,
                deathCount: this.stats.deaths
            });

            // إعادة الإحياء التلقائي إذا كان مفعلاً
            if (config.game.defaultSettings.autoRespawn) {
                setTimeout(() => {
                    if (this.isConnected && this.bot) {
                        this.bot.respawn();
                    }
                }, 2000);
            }
        });

        // عند الإحياء
        this.bot.on('respawn', () => {
            console.log(`🔄 تم إحياء البوت ${this.config.username}`);
            this.stats.respawns++;
            
            this.emit('respawn', {
                position: this.bot.entity.position,
                respawnCount: this.stats.respawns
            });
        });

        // عند حدوث خطأ
        this.bot.on('error', (error) => {
            console.error(`❌ خطأ في البوت Java ${this.config.username}: ${error.message}`);
            this.stats.errors++;

            // معالجة خاصة لأخطاء الاتصال
            if (this.isConnectionError(error)) {
                console.log(`🔌 خطأ اتصال في البوت ${this.config.username}: ${error.code || error.message}`);

                // إذا كان البوت في حالة اتصال أولي
                if (this.isConnecting && !this.isConnected) {
                    console.log(`❌ فشل الاتصال الأولي: ${error.code || error.message}`);
                    console.log(`🔌 السيرفر ${this.config.host}:${this.config.port} مطفي أو غير متصل`);
                }

                this.isConnected = false;
                this.isConnecting = false;

                // إرسال حدث انقطاع الاتصال
                this.emit('disconnected', {
                    reason: error.message,
                    error: error,
                    connectionTime: this.connectionTime,
                    disconnectionTime: new Date(),
                    duration: this.getSessionDuration()
                });

                // محاولة إعادة الاتصال إذا كان مسموح
                if (this.shouldReconnect) {
                    console.log(`🔄 بدء إعادة الاتصال بسبب خطأ الاتصال: ${error.code || error.message}`);
                    this.handleReconnect();
                }
            } else {
                // أخطاء أخرى (مثل أخطاء البروتوكول)
                console.log(`⚠️ خطأ غير متعلق بالاتصال: ${error.message}`);
                this.emit('error', error);
            }
        });

        // أحداث إضافية مفيدة
        this.bot.on('health', () => {
            this.emit('healthUpdate', {
                health: this.bot.health,
                food: this.bot.food,
                saturation: this.bot.foodSaturation
            });
        });

        this.bot.on('experience', () => {
            this.emit('experienceUpdate', {
                level: this.bot.experience.level,
                points: this.bot.experience.points,
                progress: this.bot.experience.progress
            });
        });
    }

    // التحقق من أخطاء الاتصال
    isConnectionError(error) {
        const connectionErrors = [
            'ECONNRESET',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'ENOTFOUND',
            'EHOSTUNREACH',
            'ENETUNREACH',
            'EPIPE',
            'ECONNABORTED'
        ];

        return connectionErrors.includes(error.code) ||
               error.message.includes('connect') ||
               error.message.includes('connection') ||
               error.message.includes('timeout') ||
               error.message.includes('reset');
    }

    // معالجة إعادة الاتصال
    handleReconnect() {
        // تجنب المحاولات المتعددة
        if (this.isConnecting || this.isConnected) {
            return;
        }

        if (!this.shouldReconnect || this.reconnectAttempts >= this.maxReconnectAttempts) {
            if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                console.log(`🛑 تم الوصول للحد الأقصى من محاولات إعادة الاتصال للبوت ${this.config.username}`);
                this.emit('maxReconnectAttemptsReached');
            }
            return;
        }

        this.reconnectAttempts++;
        const delay = this.calculateReconnectDelay();

        console.log(`🔄 محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.maxReconnectAttempts} خلال ${delay / 1000} ثواني...`);

        this.reconnectTimeout = setTimeout(async () => {
            // التحقق مرة أخرى قبل المحاولة
            if (this.isConnected) {
                console.log(`✅ البوت ${this.config.username} متصل بالفعل - إلغاء إعادة المحاولة`);
                return;
            }

            try {
                await this.connect();
            } catch (error) {
                console.error(`❌ فشل في إعادة الاتصال: ${error.message}`);
                // محاولة أخرى إذا فشلت
                if (this.shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.handleReconnect();
                }
            }
        }, delay);
    }

    // حساب تأخير إعادة الاتصال (ثابت 10 ثواني)
    calculateReconnectDelay() {
        return 10000; // 10 ثواني ثابتة كما طلب المستخدم
    }

    // إيقاف محاولات إعادة الاتصال
    stopReconnecting() {
        this.shouldReconnect = false;
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        console.log(`🛑 تم إيقاف محاولات إعادة الاتصال للبوت ${this.config.username}`);
    }

    // فحص إذا كان السبب يعني أن السيرفر مطفي
    isServerDownReason(reason) {
        if (!reason) return true;

        const reasonLower = reason.toLowerCase();

        // أسباب تعني أن السيرفر مطفي
        const serverDownReasons = [
            'connection refused',
            'connection timed out',
            'connection reset',
            'server closed',
            'end of stream',
            'socket hang up',
            'network error',
            'timeout',
            'econnreset',
            'econnrefused',
            'etimedout',
            'enotfound',
            'ehostunreach',
            'enetunreach',
            'epipe',
            'econnaborted',
            'econnrefused',
            'enotfound',
            'etimedout'
        ];

        return serverDownReasons.some(serverReason => reasonLower.includes(serverReason));
    }

    // فحص إذا كان السبب هو تسجيل دخول من مكان آخر
    isLoggedInOtherLocationReason(reason) {
        if (!reason) return false;
        
        const reasonLower = reason.toLowerCase();
        
        const loggedInOtherLocationReasons = [
            'logged in from another location',
            'you logged in from another location',
            'another location',
            'duplicate login',
            'already connected'
        ];

        return loggedInOtherLocationReasons.some(otherReason => reasonLower.includes(otherReason));
    }

    // إرسال رسالة في الشات
    sendMessage(message) {
        if (this.isConnected && this.bot) {
            try {
                // التحقق من طول الرسالة
                if (message.length > config.game.limits.maxMessageLength) {
                    message = message.substring(0, config.game.limits.maxMessageLength);
                }
                
                this.bot.chat(message);
                this.stats.messagesSent++;
                console.log(`📤 تم إرسال رسالة: ${message}`);
                return true;
            } catch (error) {
                console.error(`❌ خطأ في إرسال الرسالة: ${error.message}`);
                return false;
            }
        }
        return false;
    }

    // تنفيذ أمر
    executeCommand(command) {
        if (this.isConnected && this.bot) {
            try {
                // التحقق من طول الأمر
                if (command.length > config.game.limits.maxCommandLength) {
                    command = command.substring(0, config.game.limits.maxCommandLength);
                }
                
                // إضافة / إذا لم تكن موجودة
                if (!command.startsWith('/')) {
                    command = '/' + command;
                }
                
                this.bot.chat(command);
                this.stats.commandsExecuted++;
                console.log(`⚡ تم تنفيذ أمر: ${command}`);
                return true;
            } catch (error) {
                console.error(`❌ خطأ في تنفيذ الأمر: ${error.message}`);
                return false;
            }
        }
        return false;
    }

    // الحصول على معلومات البوت
    getBotInfo() {
        if (!this.bot) return null;
        
        return {
            username: this.bot.username,
            health: this.bot.health,
            food: this.bot.food,
            level: this.bot.experience?.level || 0,
            position: this.bot.entity?.position || null,
            dimension: this.bot.game?.dimension || null,
            gameMode: this.bot.game?.gameMode || null,
            ping: this.bot.player?.ping || 0
        };
    }

    // الحصول على مدة الجلسة
    getSessionDuration() {
        if (!this.connectionTime) return 0;
        const endTime = this.disconnectionTime || new Date();
        return endTime - this.connectionTime;
    }

    // الحصول على وقت التشغيل
    getUptime() {
        if (!this.connectionTime || !this.isConnected) return 0;
        return Date.now() - this.connectionTime.getTime();
    }

    // الحصول على الإحصائيات
    getStats() {
        return {
            ...this.stats,
            uptime: this.getUptime(),
            sessionDuration: this.getSessionDuration(),
            reconnectAttempts: this.reconnectAttempts,
            isConnected: this.isConnected
        };
    }

    // فحص إذا كان البوت حي
    isAlive() {
        return this.isConnected && this.bot && !this.bot.ended;
    }

    // قطع الاتصال
    async disconnect() {
        console.log(`🔌 قطع الاتصال مع البوت Java ${this.config.username}`);
        
        // إيقاف إعادة الاتصال
        this.shouldReconnect = false;
        this.isConnecting = false; // إيقاف حالة الاتصال
        this.stopReconnecting();

        if (this.bot) {
            try {
                this.bot.quit('تم إيقاف البوت');
            } catch (error) {
                console.log(`تجاهل خطأ قطع الاتصال: ${error.message}`);
            }
            this.bot = null;
        }

        this.isConnected = false;
        this.disconnectionTime = new Date();
        this.reconnectAttempts = this.maxReconnectAttempts; // منع إعادة الاتصال التلقائي
    }

    // تحديث إعدادات البوت
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }
}

module.exports = MinecraftJavaBot;
